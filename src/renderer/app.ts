// Aplicación principal del renderer - Gitator

import { GitCommit, GitLog<PERSON><PERSON>ult, QueryCommitsOptions, DirectoryInfo, DecoratorType } from '../shared/types/git-types';
import { CommitGraphGenerator } from '../shared/models/commit-graph';

// Declarar la API de Electron
declare global {
  interface Window {
    electronAPI: {
      selectDirectory: () => Promise<DirectoryInfo | null>;
      queryCommits: (repoPath: string, options: QueryCommitsOptions) => Promise<GitLogResult>;
      isGitRepository: (path: string) => Promise<boolean>;
      getRepositoryName: (path: string) => Promise<string>;
      quitApp: () => Promise<void>;
      minimizeWindow: () => Promise<void>;
      toggleMaximizeWindow: () => Promise<void>;
    };
  }
}

interface CommitDotPosition {
  commit: GitCommit;
  hash: string;
  x: number;
  y: number;
  radius: number;
  index: number;
}

class GitatorApp {
  private currentRepo: DirectoryInfo | null = null;
  private commits: GitCommit[] = [];
  private graphGenerator: CommitGraphGenerator;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  public commitDots: CommitDotPosition[] = []; // Public para tests
  private graphContainer: HTMLElement | null = null;
  private selectedCommitHash: string | null = null;
  private hashTooltip: HTMLElement | null = null;

  // Propiedades para lazy loading
  private readonly PAGE_SIZE = 100;
  private currentOffset = 0;
  private isLoadingMore = false;
  private hasMoreCommits = true;
  private isInitialLoad = true;

  constructor() {
    this.graphGenerator = new CommitGraphGenerator();
    this.initializeApp();
  }

  private initializeApp(): void {
    this.setupEventListeners();
    this.initializeCanvas();
    this.updateUI();
  }

  private setupEventListeners(): void {
    // Controles de ventana
    document.getElementById('minimize-btn')?.addEventListener('click', () => {
      window.electronAPI.minimizeWindow();
    });

    document.getElementById('maximize-btn')?.addEventListener('click', () => {
      window.electronAPI.toggleMaximizeWindow();
    });

    document.getElementById('close-btn')?.addEventListener('click', () => {
      window.electronAPI.quitApp();
    });

    // Selección de repositorio
    document.getElementById('select-repo-btn')?.addEventListener('click', () => {
      this.selectRepository();
    });

    // Controles del grafo
    document.getElementById('refresh-btn')?.addEventListener('click', () => {
      this.refreshCommits();
    });

    document.getElementById('retry-btn')?.addEventListener('click', () => {
      this.refreshCommits();
    });

    // Opciones del grafo
    document.getElementById('show-all-branches')?.addEventListener('change', () => {
      this.refreshCommits();
    });

    document.getElementById('first-parent-only')?.addEventListener('change', () => {
      this.refreshCommits();
    });
  }

  private initializeCanvas(): void {
    this.canvas = document.getElementById('commit-graph') as HTMLCanvasElement;
    this.graphContainer = document.getElementById('commit-graph-container');
    this.commitList = document.getElementById('commit-list');
    this.hashTooltip = document.getElementById('commit-hash-tooltip');

    // Configurar scroll listener para lazy loading
    this.setupScrollListener();

    if (this.canvas) {
      this.ctx = this.canvas.getContext('2d');
      this.setupCanvasEvents();
      this.setupScrollSync();
      this.setupTooltipEvents();
    }
  }

  private setupScrollListener(): void {
    if (!this.commitList) return;

    this.commitList.addEventListener('scroll', () => {
      if (this.isLoadingMore || !this.hasMoreCommits) return;

      const scrollTop = this.commitList!.scrollTop;
      const scrollHeight = this.commitList!.scrollHeight;
      const clientHeight = this.commitList!.clientHeight;

      // Cargar más cuando esté cerca del final (200px antes del final)
      const threshold = 200;
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        this.loadMoreCommits();
      }
    });
  }

  private setupCanvasEvents(): void {
    if (!this.canvas) return;

    // Redimensionar canvas
    const resizeCanvas = () => {
      if (!this.canvas || !this.ctx) return;
      if (this.commits.length > 0) {
        this.drawCommitGraph();
      }
    };

    window.addEventListener('resize', resizeCanvas);

    // Click en canvas para seleccionar commit
    this.canvas.addEventListener('click', (event) => {
      this.handleCanvasClick(event);
    });

    // Eventos de mouse para cambiar cursor
    this.canvas.addEventListener('mousemove', (event) => {
      this.handleCanvasMouseMove(event);
    });

    this.canvas.addEventListener('mouseleave', () => {
      this.canvas?.classList.remove('pointer');
    });
  }

  private setupScrollSync(): void {
    if (!this.graphContainer || !this.commitList) return;

    // Sincronizar scroll del grafo con la lista de commits
    this.graphContainer.addEventListener('scroll', () => {
      if (this.isScrollSyncing) return;
      this.isScrollSyncing = true;
      this.commitList!.scrollTop = this.graphContainer!.scrollTop;
      requestAnimationFrame(() => {
        this.isScrollSyncing = false;
      });
    });

    // Sincronizar scroll de la lista de commits con el grafo
    this.commitList.addEventListener('scroll', () => {
      if (this.isScrollSyncing) return;
      this.isScrollSyncing = true;
      this.graphContainer!.scrollTop = this.commitList!.scrollTop;
      requestAnimationFrame(() => {
        this.isScrollSyncing = false;
      });
    });
  }

  private setupTooltipEvents(): void {
    if (!this.graphContainer) return;

    // Ocultar tooltip al hacer scroll
    this.graphContainer.addEventListener('scroll', () => {
      this.hideHashTooltip();
    });

    // Ocultar tooltip al hacer click en la lista de commits
    if (this.commitList) {
      this.commitList.addEventListener('click', () => {
        this.hideHashTooltip();
      });
    }
  }

  private async selectRepository(): Promise<void> {
    try {
      const directory = await window.electronAPI.selectDirectory();
      if (!directory) return;

      this.currentRepo = directory;
      this.updateRepositoryInfo();

      if (directory.isGitRepository) {
        await this.loadCommits();
      } else {
        this.showError('El directorio seleccionado no es un repositorio Git válido');
      }
    } catch (error) {
      this.showError(`Error al seleccionar repositorio: ${error}`);
    }
  }

  private updateRepositoryInfo(): void {
    const repoNameEl = document.getElementById('repo-name');
    const repoInfoEl = document.getElementById('repo-info');
    const repoPathEl = document.getElementById('repo-path');
    const repoStatusEl = document.getElementById('repo-status');
    const refreshBtn = document.getElementById('refresh-btn') as HTMLButtonElement;

    if (this.currentRepo) {
      if (repoNameEl) repoNameEl.textContent = this.currentRepo.name;
      if (repoInfoEl) repoInfoEl.classList.remove('hidden');
      if (repoPathEl) repoPathEl.textContent = this.currentRepo.path;

      if (repoStatusEl) {
        repoStatusEl.textContent = this.currentRepo.isGitRepository ?
          '✓ Repositorio Git válido' : '✗ No es un repositorio Git';
        repoStatusEl.className = `repo-status ${this.currentRepo.isGitRepository ? 'valid' : 'invalid'}`;
      }

      if (refreshBtn) refreshBtn.disabled = !this.currentRepo.isGitRepository;
    } else {
      if (repoNameEl) repoNameEl.textContent = '';
      if (repoInfoEl) repoInfoEl.classList.add('hidden');
      if (refreshBtn) refreshBtn.disabled = true;
    }
  }

  private async loadCommits(): Promise<void> {
    if (!this.currentRepo?.isGitRepository) return;

    this.hideHashTooltip(); // Ocultar tooltip al cargar nuevos commits

    // Reset para carga inicial
    if (this.isInitialLoad) {
      this.commits = [];
      this.currentOffset = 0;
      this.hasMoreCommits = true;
      this.showLoading();
    }

    try {
      const options = this.getQueryOptions();
      const result = await window.electronAPI.queryCommits(this.currentRepo.path, options);

      if (result.success) {
        // Log de la carga
        if (result.commits.length > 0) {
          const loadType = this.isInitialLoad ? 'Carga Inicial' : 'Lazy Load';
          console.log(`[${loadType}] Cargando ${result.commits.length} commits desde: ${result.commits[0].sha} (${result.commits[0].subject})`);
        }

        if (this.isInitialLoad) {
          this.commits = result.commits;
        } else {
          this.commits = [...this.commits, ...result.commits];
        }

        // Verificar si hay más commits
        this.hasMoreCommits = result.commits.length === this.PAGE_SIZE;
        this.currentOffset += result.commits.length;

        this.generateAndDrawGraph();
        this.updateCommitList();
        this.updateCommitCount();

        if (this.isInitialLoad) {
          this.showGraph();
          this.isInitialLoad = false;
        }

        this.setStatus(`Cargados ${this.commits.length} commits`);
      } else {
        this.showError(result.error || 'Error desconocido al cargar commits');
      }
    } catch (error) {
      this.showError(`Error al cargar commits: ${error}`);
    }
  }

  private getQueryOptions(): QueryCommitsOptions {
    const showAllBranches = (document.getElementById('show-all-branches') as HTMLInputElement)?.checked ?? true;

    return {
      maxCount: this.PAGE_SIZE,
      skip: this.currentOffset,
      all: showAllBranches,
      needFindHead: this.isInitialLoad
    };
  }

  private async loadMoreCommits(): Promise<void> {
    if (this.isLoadingMore || !this.hasMoreCommits || !this.currentRepo?.isGitRepository) {
      return;
    }

    this.isLoadingMore = true;
    this.showLoadingMore();
    this.setStatus('Cargando más commits...');

    try {
      const options = this.getQueryOptions();
      const result = await window.electronAPI.queryCommits(this.currentRepo.path, options);

      if (result.success) {
        // Log del lazy loading
        if (result.commits.length > 0) {
          console.log(`[Lazy Load] Cargando ${result.commits.length} commits desde: ${result.commits[0].sha} (${result.commits[0].subject})`);
        }

        // Añadir nuevos commits al final
        this.commits = [...this.commits, ...result.commits];

        // Verificar si hay más commits
        this.hasMoreCommits = result.commits.length === this.PAGE_SIZE;
        this.currentOffset += result.commits.length;

        // Regenerar el grafo completo
        this.generateAndDrawGraph();
        this.updateCommitList();
        this.updateCommitCount();

        this.setStatus(`Cargados ${this.commits.length} commits`);
      } else {
        this.setStatus('Error al cargar más commits');
      }
    } catch (error) {
      this.setStatus(`Error al cargar más commits: ${error}`);
    } finally {
      this.isLoadingMore = false;
      this.hideLoadingMore();
    }
  }

  private generateAndDrawGraph(): void {
    if (this.commits.length === 0) return;

    const firstParentOnly = (document.getElementById('first-parent-only') as HTMLInputElement)?.checked ?? false;
    const graph = this.graphGenerator.parse(this.commits, firstParentOnly);

    this.drawCommitGraph();
  }

  private drawCommitGraph(): void {
    if (!this.canvas || !this.ctx || this.commits.length === 0) return;

    // Ajustar la altura del canvas según el número de commits
    const commitRowHeight = 60;
    const totalHeight = this.commits.length * commitRowHeight;
    const containerRect = this.graphContainer?.getBoundingClientRect();
    const canvasWidth = containerRect?.width || 300;

    // Configurar el tamaño del canvas
    this.canvas.width = canvasWidth * window.devicePixelRatio;
    this.canvas.height = totalHeight * window.devicePixelRatio;
    this.canvas.style.width = `${canvasWidth}px`;
    this.canvas.style.height = `${totalHeight}px`;
    this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    this.ctx.clearRect(0, 0, canvasWidth, totalHeight);

    // Limpiar posiciones de dots anteriores
    this.commitDots = [];

    const firstParentOnly = (document.getElementById('first-parent-only') as HTMLInputElement)?.checked ?? false;
    const graph = this.graphGenerator.parse(this.commits, firstParentOnly);
    const colors = this.graphGenerator.getColors();

    // Dibujar paths (líneas de conexión)
    for (const path of graph.paths) {
      this.drawPath(path, colors);
    }

    // Dibujar links (conexiones de merge)
    for (const link of graph.links) {
      this.drawLink(link, colors);
    }

    // Dibujar dots (puntos de commit) y almacenar sus posiciones
    graph.dots.forEach((dot, index) => {
      // Encontrar el commit correspondiente a este dot
      // Los dots están en el mismo orden que los commits procesados
      if (index < this.commits.length) {
        this.drawDot(dot, colors, this.commits[index], index);
      }
    });
  }

  private drawPath(path: any, colors: string[]): void {
    if (!this.ctx || path.points.length < 2) return;

    this.ctx.strokeStyle = colors[path.color % colors.length];
    this.ctx.lineWidth = 2;
    this.ctx.globalAlpha = path.isMerged ? 1 : this.graphGenerator.getOpacityForNotMerged();

    const commitRowHeight = 60;

    this.ctx.beginPath();
    // Convertir coordenadas del grafo a coordenadas alineadas con las filas
    const startY = (path.points[0].y * commitRowHeight) + (commitRowHeight / 2);
    this.ctx.moveTo(path.points[0].x, startY);

    for (let i = 1; i < path.points.length; i++) {
      const y = (path.points[i].y * commitRowHeight) + (commitRowHeight / 2);
      this.ctx.lineTo(path.points[i].x, y);
    }

    this.ctx.stroke();
    this.ctx.globalAlpha = 1;
  }

  private drawLink(link: any, colors: string[]): void {
    if (!this.ctx) return;

    this.ctx.strokeStyle = colors[link.color % colors.length];
    this.ctx.lineWidth = 2;
    this.ctx.globalAlpha = link.isMerged ? 1 : this.graphGenerator.getOpacityForNotMerged();

    const commitRowHeight = 60;

    this.ctx.beginPath();
    // Convertir coordenadas del grafo a coordenadas alineadas con las filas
    const startY = (link.start.y * commitRowHeight) + (commitRowHeight / 2);
    const controlY = (link.control.y * commitRowHeight) + (commitRowHeight / 2);
    const endY = (link.end.y * commitRowHeight) + (commitRowHeight / 2);

    this.ctx.moveTo(link.start.x, startY);
    this.ctx.quadraticCurveTo(
      link.control.x, controlY,
      link.end.x, endY
    );
    this.ctx.stroke();
    this.ctx.globalAlpha = 1;
  }

  private drawDot(dot: any, colors: string[], commit: GitCommit, commitIndex: number): void {
    if (!this.ctx) return;

    const x = dot.center.x;
    // Calcular Y basado en la posición de la fila del commit en la lista
    const commitRowHeight = 60; // Debe coincidir con el CSS .commit-item height
    const y = (commitIndex * commitRowHeight) + (commitRowHeight / 2); // Centro de la fila
    const radius = dot.type === 1 ? 5 : 4; // Head dots are larger

    // Almacenar posición del dot para detección de clics
    const dotInfo = {
      commit: commit,
      hash: commit.sha,
      x,
      y,
      radius: radius + 2, // Añadir un poco más de área para facilitar el clic
      index: commitIndex
    };
    this.commitDots.push(dotInfo);

    this.ctx.fillStyle = colors[dot.color % colors.length];
    this.ctx.globalAlpha = dot.isMerged ? 1 : this.graphGenerator.getOpacityForNotMerged();

    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fill();

    // Outline for better visibility
    this.ctx.strokeStyle = '#1e1e1e';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();

    this.ctx.globalAlpha = 1;
  }

  private updateCommitList(): void {
    const commitListEl = document.getElementById('commit-list');
    if (!commitListEl) return;

    commitListEl.innerHTML = '';

    for (const commit of this.commits) {
      const commitEl = this.createCommitElement(commit);
      commitListEl.appendChild(commitEl);
    }
  }

  private createCommitElement(commit: GitCommit): HTMLElement {
    const commitEl = document.createElement('div');
    commitEl.className = 'commit-item';
    commitEl.dataset.sha = commit.sha;

    const infoEl = document.createElement('div');
    infoEl.className = 'commit-info';

    const subjectEl = document.createElement('div');
    subjectEl.className = 'commit-subject';
    subjectEl.textContent = commit.subject;

    const metaEl = document.createElement('div');
    metaEl.className = 'commit-meta';

    const shaEl = document.createElement('span');
    shaEl.className = 'commit-sha';
    shaEl.textContent = commit.sha.substring(0, 8);

    const authorEl = document.createElement('span');
    authorEl.textContent = commit.author.name;

    const timeEl = document.createElement('span');
    timeEl.textContent = commit.authorTimeShortStr;

    metaEl.appendChild(shaEl);
    metaEl.appendChild(authorEl);
    metaEl.appendChild(timeEl);

    infoEl.appendChild(subjectEl);
    infoEl.appendChild(metaEl);

    commitEl.appendChild(infoEl);

    // Agregar decoradores
    if (commit.hasDecorators) {
      const decoratorsEl = document.createElement('div');
      decoratorsEl.className = 'commit-decorators';

      for (const decorator of commit.decorators) {
        const decoratorEl = document.createElement('span');
        decoratorEl.className = `decorator ${this.getDecoratorClass(decorator.type)}`;
        decoratorEl.textContent = decorator.name;
        decoratorsEl.appendChild(decoratorEl);
      }

      commitEl.appendChild(decoratorsEl);
    }

    commitEl.addEventListener('click', () => {
      this.selectCommit(commit.sha);
    });

    return commitEl;
  }

  private getDecoratorClass(type: DecoratorType): string {
    switch (type) {
      case DecoratorType.CurrentBranchHead:
      case DecoratorType.CurrentCommitHead:
        return 'head';
      case DecoratorType.LocalBranchHead:
        return 'branch';
      case DecoratorType.RemoteBranchHead:
        return 'remote';
      case DecoratorType.Tag:
        return 'tag';
      default:
        return 'branch';
    }
  }

  private selectCommit(sha: string): void {
    // Remover selección anterior
    document.querySelectorAll('.commit-item.selected').forEach(el => {
      el.classList.remove('selected');
    });

    // Seleccionar nuevo commit
    const commitEl = document.querySelector(`[data-sha="${sha}"]`) as HTMLElement;
    if (commitEl) {
      commitEl.classList.add('selected');

      // Hacer scroll automático para que el commit sea visible
      this.scrollToCommit(commitEl);
    }
  }

  private scrollToCommit(commitEl: HTMLElement): void {
    if (!this.commitList) return;

    const listRect = this.commitList.getBoundingClientRect();
    const commitRect = commitEl.getBoundingClientRect();

    // Calcular si el commit está visible
    const isVisible = (
      commitRect.top >= listRect.top &&
      commitRect.bottom <= listRect.bottom
    );

    if (!isVisible) {
      // Calcular la posición de scroll necesaria para centrar el commit
      const commitOffsetTop = commitEl.offsetTop;
      const listHeight = this.commitList.clientHeight;
      const commitHeight = commitEl.clientHeight;

      // Centrar el commit en la lista
      const scrollTop = commitOffsetTop - (listHeight / 2) + (commitHeight / 2);

      // Hacer scroll suave
      this.commitList.scrollTo({
        top: Math.max(0, scrollTop),
        behavior: 'smooth'
      });
    }
  }

  private getCanvasCoordinates(event: MouseEvent): { x: number, y: number, canvasY: number } {
    if (!this.canvas || !this.graphContainer) return { x: 0, y: 0, canvasY: 0 };

    const rect = this.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const canvasY = y + this.graphContainer.scrollTop; // Coordenada Y en el canvas completo

    return { x, y, canvasY };
  }

  private findCommitByRow(canvasY: number): { commit: GitCommit, hasDot: boolean } | null {
    const commitRowHeight = 60;
    const row = Math.floor(canvasY / commitRowHeight);

    console.log(this.commitDots)

    if (row >= 0 && row < this.commits.length) {
      const commit = this.commits[row];
      // Verificar si este commit tiene un punto en el grafo
      const hasDot = this.commitDots.some(dot => dot.hash === commit.sha);

      return { commit, hasDot };
    }
    return null;
  }



  private handleCanvasMouseMove(event: MouseEvent): void {
    if (!this.canvas) return;

    const { canvasY } = this.getCanvasCoordinates(event);
    const result = this.findCommitByRow(canvasY);

    // Cambiar cursor según si está sobre una fila que tiene punto
    if (result && result.hasDot) {
      this.canvas.classList.add('pointer');
    } else {
      this.canvas.classList.remove('pointer');
    }
  }

  private handleCanvasClick(event: MouseEvent): void {
    if (!this.canvas || !this.graphContainer) return;


    const { x, y, canvasY } = this.getCanvasCoordinates(event);

    const result = this.findCommitByRow(canvasY);

    if (result) {
      // Seleccionar el commit
      this.selectCommit(result.commit.sha);

      if (result.hasDot) {
        // Si la fila tiene un punto: mostrar tooltip
        this.showHashTooltip(result.commit, x, y);
      } else {
        // Si la fila no tiene punto: ocultar tooltip
        this.hideHashTooltip();
      }
    } else {
      // Click fuera de cualquier fila: ocultar tooltip
      this.hideHashTooltip();
    }
  }

  private showHashTooltip(commit: GitCommit, x: number, y: number): void {
    if (!this.hashTooltip || !this.graphContainer) return;

    // Mostrar solo los primeros 6 caracteres del hash
    this.hashTooltip.textContent = commit.sha.substring(0, 6);

    // Posicionar el tooltip
    const containerRect = this.graphContainer.getBoundingClientRect();
    const tooltipWidth = 80; // Ancho aproximado del tooltip (6 chars + padding)
    const tooltipHeight = 40; // Altura aproximada del tooltip

    // Calcular posición X (centrado en el punto, pero sin salirse del contenedor)
    let tooltipX = x - (tooltipWidth / 2);
    tooltipX = Math.max(10, Math.min(tooltipX, containerRect.width - tooltipWidth - 10));

    // Calcular posición Y (arriba del punto)
    let tooltipY = y - tooltipHeight - 10;
    if (tooltipY < 10) {
      tooltipY = y + 20; // Si no cabe arriba, ponerlo abajo
    }

    this.hashTooltip.style.left = `${tooltipX}px`;
    this.hashTooltip.style.top = `${tooltipY}px`;

    // Mostrar el tooltip
    this.hashTooltip.classList.add('visible');
  }

  private hideHashTooltip(): void {
    if (!this.hashTooltip) return;
    this.hashTooltip.classList.remove('visible');
  }

  private updateCommitCount(): void {
    const countEl = document.getElementById('commit-count');
    if (countEl) {
      countEl.textContent = `${this.commits.length} commits`;
    }
  }

  private async refreshCommits(): Promise<void> {
    if (this.currentRepo?.isGitRepository) {
      // Reset del estado para una nueva carga
      this.isInitialLoad = true;
      this.currentOffset = 0;
      this.hasMoreCommits = true;
      this.isLoadingMore = false;
      await this.loadCommits();
    }
  }

  private showLoading(): void {
    this.hideAllViews();
    document.getElementById('loading')?.classList.remove('hidden');
    this.setStatus('Cargando commits...');
  }

  private showGraph(): void {
    this.hideAllViews();
    document.getElementById('graph-container')?.classList.remove('hidden');
  }

  private showLoadingMore(): void {
    document.getElementById('loading-more')?.classList.remove('hidden');
  }

  private hideLoadingMore(): void {
    document.getElementById('loading-more')?.classList.add('hidden');
  }

  private showError(message: string): void {
    this.hideAllViews();
    const errorEl = document.getElementById('error');
    const errorMessageEl = document.getElementById('error-message');

    if (errorEl) errorEl.classList.remove('hidden');
    if (errorMessageEl) errorMessageEl.textContent = message;

    this.setStatus(`Error: ${message}`);
  }

  private hideAllViews(): void {
    document.getElementById('loading')?.classList.add('hidden');
    document.getElementById('no-repo')?.classList.add('hidden');
    document.getElementById('error')?.classList.add('hidden');
    document.getElementById('graph-container')?.classList.add('hidden');
  }

  private updateUI(): void {
    if (this.currentRepo?.isGitRepository && this.commits.length > 0) {
      this.showGraph();
    } else if (this.currentRepo) {
      this.showError('No es un repositorio Git válido');
    } else {
      document.getElementById('no-repo')?.classList.remove('hidden');
    }
  }

  private setStatus(message: string): void {
    const statusEl = document.getElementById('status-text');
    if (statusEl) {
      statusEl.textContent = message;
    }
  }
}

// Inicializar la aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
  const app = new GitatorApp();
  // Exponer la instancia para tests
  (window as any).gitatorApp = app;
});
