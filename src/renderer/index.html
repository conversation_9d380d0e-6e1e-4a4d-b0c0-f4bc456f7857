<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gitator - Git Branch Visualizer</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <!-- Header con controles de ventana -->
        <header class="app-header">
            <div class="title-bar">
                <div class="title">
                    <h1>Gitator</h1>
                    <span id="repo-name" class="repo-name"></span>
                </div>
                <div class="window-controls">
                    <button id="minimize-btn" class="window-control-btn" title="Minimizar">−</button>
                    <button id="maximize-btn" class="window-control-btn" title="Maximizar">□</button>
                    <button id="close-btn" class="window-control-btn close" title="Cerrar">×</button>
                </div>
            </div>
        </header>

        <!-- Contenido principal -->
        <main class="app-main">
            <!-- Sidebar para selección de repositorio -->
            <aside class="sidebar">
                <div class="repo-selector">
                    <button id="select-repo-btn" class="select-repo-button">
                        📁 Seleccionar Repositorio
                    </button>
                    <div id="repo-info" class="repo-info hidden">
                        <div class="repo-path" id="repo-path"></div>
                        <div class="repo-status" id="repo-status"></div>
                    </div>
                </div>

                <!-- Controles del grafo -->
                <div class="graph-controls">
                    <h3>Opciones del Grafo</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="show-all-branches" checked>
                            Mostrar todas las ramas
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="first-parent-only">
                            Solo primer padre
                        </label>
                    </div>

                    <button id="refresh-btn" class="refresh-button" disabled>
                        🔄 Actualizar
                    </button>
                </div>
            </aside>

            <!-- Área principal del grafo -->
            <section class="graph-area">
                <div id="loading" class="loading hidden">
                    <div class="spinner"></div>
                    <p>Cargando commits...</p>
                </div>

                <div id="no-repo" class="no-repo">
                    <div class="no-repo-content">
                        <h2>🌳 Bienvenido a Gitator</h2>
                        <p>Selecciona un repositorio Git para visualizar su grafo de commits</p>
                        <button class="select-repo-button" onclick="document.getElementById('select-repo-btn').click()">
                            📁 Seleccionar Repositorio
                        </button>
                    </div>
                </div>

                <div id="error" class="error hidden">
                    <div class="error-content">
                        <h3>❌ Error</h3>
                        <p id="error-message"></p>
                        <button id="retry-btn" class="retry-button">Reintentar</button>
                    </div>
                </div>

                <!-- Canvas para el grafo de commits -->
                <div id="graph-container" class="graph-container hidden">
                    <div class="graph-header">
                        <div class="graph-info">
                            <span id="commit-count">0 commits</span>
                        </div>
                    </div>
                    <div class="graph-content">
                        <div class="commit-graph-container" id="commit-graph-container">
                            <canvas id="commit-graph" class="commit-graph"></canvas>
                            <div id="commit-hash-tooltip" class="commit-hash-tooltip"></div>
                        </div>
                        <div id="loading-more" class="loading-more hidden">
                            <div class="spinner-small"></div>
                            <span>Cargando más commits...</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Status bar -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="status-text">Listo</span>
            </div>
            <div class="status-right">
                <span id="zoom-level">100%</span>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
