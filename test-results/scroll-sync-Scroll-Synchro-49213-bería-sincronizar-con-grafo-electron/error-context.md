# Test info

- Name: Scroll Synchronization - Tests de Sincronización >> scroll en lista de commits debería sincronizar con grafo
- Location: /Users/<USER>/repositorios/gitator/tests/scroll-sync.test.js:58:3

# Error details

```
Error: page.waitForSelector: Target page, context or browser has been closed
Call log:
  - waiting for locator('#graph-container:not(.hidden)') to be visible

    at ElectronTestHelper.waitForRepoLoad (/Users/<USER>/repositorios/gitator/tests/setup.js:131:18)
    at ElectronTestHelper.selectTestRepo (/Users/<USER>/repositorios/gitator/tests/setup.js:160:5)
    at /Users/<USER>/repositorios/gitator/tests/scroll-sync.test.js:18:5
```

# Test source

```ts
   31 |     // Los mocks están en el proceso principal, no necesitamos configurar nada aquí
   32 |
   33 |     return { electronApp: this.electronApp, page: this.page };
   34 |   }
   35 |
   36 |   async close() {
   37 |     if (this.electronApp) {
   38 |       await this.electronApp.close();
   39 |     }
   40 |   }
   41 |
   42 |   // Configurar mocks básicos
   43 |   async setupMocks(page) {
   44 |     await page.evaluate(() => {
   45 |       // Verificar que electronAPI existe
   46 |       if (typeof window.electronAPI === 'undefined') {
   47 |         console.log('electronAPI no existe, creando mock...');
   48 |         window.electronAPI = {};
   49 |       }
   50 |
   51 |       // Mock por defecto de selectDirectory
   52 |       window.electronAPI.selectDirectory = async () => ({
   53 |         path: '/Users/<USER>/repositorios/vscode',
   54 |         name: 'vscode',
   55 |         isGitRepository: true
   56 |       });
   57 |
   58 |       // Mock por defecto de queryCommits
   59 |       window.electronAPI.queryCommits = async () => ({
   60 |         success: true,
   61 |         commits: [
   62 |           {
   63 |             sha: 'abcdef1234567890abcdef1234567890abcdef12',
   64 |             subject: 'Initial commit',
   65 |             author: { name: 'Test Author', email: '<EMAIL>' },
   66 |             authorTimeShortStr: '1 day ago',
   67 |             parents: [],
   68 |             decorators: [{ type: 1, name: 'main' }],
   69 |             hasDecorators: true,
   70 |             isCurrentHead: true,
   71 |             isMerged: false
   72 |           },
   73 |           {
   74 |             sha: '1234567890abcdef1234567890abcdef12345678',
   75 |             subject: 'Add feature X',
   76 |             author: { name: 'Test Author', email: '<EMAIL>' },
   77 |             authorTimeShortStr: '2 days ago',
   78 |             parents: ['abcdef1234567890abcdef1234567890abcdef12'],
   79 |             decorators: [],
   80 |             hasDecorators: false,
   81 |             isCurrentHead: false,
   82 |             isMerged: false
   83 |           }
   84 |         ],
   85 |         error: null
   86 |       });
   87 |
   88 |       console.log('Mocks configurados correctamente');
   89 |     });
   90 |   }
   91 |
   92 |   // Helper para esperar a que el repositorio se cargue
   93 |   async waitForRepoLoad(page) {
   94 |     // Debug: verificar qué elementos están visibles
   95 |     await page.waitForTimeout(2000); // Esperar un poco
   96 |
   97 |     const debugInfo = await page.evaluate(() => {
   98 |       const graphContainer = document.getElementById('graph-container');
   99 |       const noRepo = document.getElementById('no-repo');
  100 |       const error = document.getElementById('error');
  101 |       const loading = document.getElementById('loading');
  102 |
  103 |       return {
  104 |         graphContainer: {
  105 |           exists: !!graphContainer,
  106 |           visible: graphContainer ? !graphContainer.classList.contains('hidden') : false,
  107 |           classes: graphContainer ? graphContainer.className : 'not found'
  108 |         },
  109 |         noRepo: {
  110 |           exists: !!noRepo,
  111 |           visible: noRepo ? !noRepo.classList.contains('hidden') : false,
  112 |           classes: noRepo ? noRepo.className : 'not found'
  113 |         },
  114 |         error: {
  115 |           exists: !!error,
  116 |           visible: error ? !error.classList.contains('hidden') : false,
  117 |           classes: error ? error.className : 'not found'
  118 |         },
  119 |         loading: {
  120 |           exists: !!loading,
  121 |           visible: loading ? !loading.classList.contains('hidden') : false,
  122 |           classes: loading ? loading.className : 'not found'
  123 |         }
  124 |       };
  125 |     });
  126 |
  127 |     console.log('Debug info:', JSON.stringify(debugInfo, null, 2));
  128 |
  129 |     // Intentar esperar por el graph-container (timeout largo para selección manual)
  130 |     try {
> 131 |       await page.waitForSelector('#graph-container:not(.hidden)', { timeout: 30000 });
      |                  ^ Error: page.waitForSelector: Target page, context or browser has been closed
  132 |       await page.waitForSelector('#commit-list .commit-item', { timeout: 10000 });
  133 |       console.log('✅ Repositorio cargado exitosamente!');
  134 |     } catch (error) {
  135 |       console.log('Error esperando elementos:', error.message);
  136 |       throw error;
  137 |     }
  138 |   }
  139 |
  140 |   // Helper para seleccionar un repositorio de test
  141 |   async selectTestRepo(page, repoPath = null) {
  142 |     // Configurar mock automático para evitar selección manual repetitiva
  143 |     await page.evaluate(() => {
  144 |       // Mock del electronAPI.selectDirectory para devolver automáticamente el repo de test
  145 |       if (window.electronAPI) {
  146 |         window.electronAPI.selectDirectory = async () => ({
  147 |           path: '/Users/<USER>/repositorios/vscode',
  148 |           name: 'vscode',
  149 |           isGitRepository: true
  150 |         });
  151 |       }
  152 |     });
  153 |
  154 |     // Hacer click en el botón
  155 |     await page.click('#select-repo-btn');
  156 |
  157 |     console.log('🔧 Usando repositorio de test automático: /Users/<USER>/repositorios/vscode');
  158 |
  159 |     // Esperar a que se cargue el repositorio
  160 |     await this.waitForRepoLoad(page);
  161 |   }
  162 |
  163 |   // Helper para obtener información del canvas
  164 |   async getCanvasInfo(page) {
  165 |     return await page.evaluate(() => {
  166 |       const canvas = document.getElementById('commit-graph');
  167 |       const container = document.getElementById('commit-graph-container');
  168 |       const graphContainer = document.getElementById('graph-container');
  169 |
  170 |       if (!canvas || !container) {
  171 |         return {
  172 |           visible: false,
  173 |           canvas: null,
  174 |           container: null
  175 |         };
  176 |       }
  177 |
  178 |       const rect = canvas.getBoundingClientRect();
  179 |       const containerRect = container.getBoundingClientRect();
  180 |       const isVisible = !graphContainer?.classList.contains('hidden') &&
  181 |                        rect.width > 0 && rect.height > 0;
  182 |
  183 |       return {
  184 |         visible: isVisible,
  185 |         width: canvas.width,
  186 |         height: canvas.height,
  187 |         canvas: {
  188 |           width: canvas.width,
  189 |           height: canvas.height,
  190 |           styleWidth: canvas.style.width,
  191 |           styleHeight: canvas.style.height,
  192 |           rect: rect
  193 |         },
  194 |         container: {
  195 |           rect: containerRect,
  196 |           scrollTop: container.scrollTop,
  197 |           scrollHeight: container.scrollHeight
  198 |         }
  199 |       };
  200 |     });
  201 |   }
  202 |
  203 |   // Helper para obtener información de commits
  204 |   async getCommitsInfo(page) {
  205 |     return await page.evaluate(() => {
  206 |       const commitItems = document.querySelectorAll('#commit-list .commit-item');
  207 |       const commitList = document.getElementById('commit-list');
  208 |
  209 |       return {
  210 |         count: commitItems.length,
  211 |         scrollTop: commitList ? commitList.scrollTop : 0,
  212 |         scrollHeight: commitList ? commitList.scrollHeight : 0,
  213 |         commits: Array.from(commitItems).map((item, index) => ({
  214 |           index,
  215 |           sha: item.dataset.sha,
  216 |           subject: item.querySelector('.commit-subject')?.textContent,
  217 |           selected: item.classList.contains('selected'),
  218 |           rect: item.getBoundingClientRect()
  219 |         }))
  220 |       };
  221 |     });
  222 |   }
  223 |
  224 |   // Helper para simular scroll
  225 |   async scrollCommitList(page, scrollTop) {
  226 |     await page.evaluate((scrollTop) => {
  227 |       const commitList = document.getElementById('commit-list');
  228 |       if (commitList) {
  229 |         commitList.scrollTop = scrollTop;
  230 |         commitList.dispatchEvent(new Event('scroll'));
  231 |       }
```