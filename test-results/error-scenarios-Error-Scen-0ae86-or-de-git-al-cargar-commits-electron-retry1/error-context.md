# Test info

- Name: <PERSON><PERSON><PERSON> - Tests de Escenarios de Error >> debería manejar error de git al cargar commits
- Location: /Users/<USER>/repositorios/gitator/tests/error-scenarios.test.js:50:3

# Error details

```
Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

Locator: locator('#error')
Expected: visible
Received: hidden
Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('#error')
    13 × locator resolved to <div id="error" class="error hidden">…</div>
       - unexpected value "hidden"

    at /Users/<USER>/repositorios/gitator/tests/error-scenarios.test.js:70:42
```

# Test source

```ts
   1 | // Tests para escenarios de error específicos que hemos encontrado
   2 | const { test, expect, ElectronTestHelper } = require('./setup');
   3 |
   4 | test.describe('Error Scenarios - Tests de Escenarios de Error', () => {
   5 |   let helper;
   6 |   let electronApp;
   7 |   let page;
   8 |
   9 |   test.beforeEach(async () => {
   10 |     helper = new ElectronTestHelper();
   11 |     const result = await helper.launch();
   12 |     electronApp = result.electronApp;
   13 |     page = result.page;
   14 |   });
   15 |
   16 |   test.afterEach(async () => {
   17 |     await helper.close();
   18 |   });
   19 |
   20 |   test('debería manejar repositorio sin commits', async () => {
   21 |     // Mock de un repositorio vacío
   22 |     await page.evaluate(() => {
   23 |       window.electronAPI.selectDirectory = async () => ({
   24 |         path: '/tmp/empty-git-repo',
   25 |         name: 'empty-git-repo',
   26 |         isGitRepository: true
   27 |       });
   28 |       
   29 |       window.electronAPI.queryCommits = async () => ({
   30 |         success: true,
   31 |         commits: [],
   32 |         error: null
   33 |       });
   34 |     });
   35 |
   36 |     await page.click('#select-repo-btn');
   37 |     await page.waitForTimeout(1000);
   38 |
   39 |     // Debería mostrar el grafo pero sin commits
   40 |     await expect(page.locator('#graph-container')).toBeVisible();
   41 |     
   42 |     const commitsInfo = await helper.getCommitsInfo(page);
   43 |     expect(commitsInfo.count).toBe(0);
   44 |     
   45 |     // El contador debería mostrar 0 commits
   46 |     const commitCount = await page.locator('#commit-count').textContent();
   47 |     expect(commitCount).toBe('0 commits');
   48 |   });
   49 |
   50 |   test('debería manejar error de git al cargar commits', async () => {
   51 |     // Mock de error de git
   52 |     await page.evaluate(() => {
   53 |       window.electronAPI.selectDirectory = async () => ({
   54 |         path: '/tmp/broken-git-repo',
   55 |         name: 'broken-git-repo',
   56 |         isGitRepository: true
   57 |       });
   58 |       
   59 |       window.electronAPI.queryCommits = async () => ({
   60 |         success: false,
   61 |         commits: [],
   62 |         error: 'fatal: not a git repository'
   63 |       });
   64 |     });
   65 |
   66 |     await page.click('#select-repo-btn');
   67 |     await page.waitForTimeout(1000);
   68 |
   69 |     // Debería mostrar error
>  70 |     await expect(page.locator('#error')).toBeVisible();
      |                                          ^ Error: Timed out 10000ms waiting for expect(locator).toBeVisible()
   71 |     
   72 |     const errorMessage = await page.locator('#error-message').textContent();
   73 |     expect(errorMessage).toContain('fatal: not a git repository');
   74 |   });
   75 |
   76 |   test('debería manejar clicks fuera del área de dots', async () => {
   77 |     await helper.selectTestRepo(page);
   78 |     
   79 |     // Click en área vacía del canvas (lejos de cualquier dot)
   80 |     await helper.clickCanvas(page, 200, 200);
   81 |     await page.waitForTimeout(500);
   82 |     
   83 |     // No debería haber tooltip visible
   84 |     const tooltipInfo = await helper.getTooltipInfo(page);
   85 |     expect(tooltipInfo.visible).toBe(false);
   86 |     
   87 |     // No debería haber commits seleccionados por el click en área vacía
   88 |     const commitsInfo = await helper.getCommitsInfo(page);
   89 |     const selectedCommits = commitsInfo.commits.filter(c => c.selected);
   90 |     // Puede haber un commit seleccionado de antes, pero no debería cambiar por el click vacío
   91 |   });
   92 |
   93 |   test('debería manejar redimensionado extremo de ventana', async () => {
   94 |     await helper.selectTestRepo(page);
   95 |     
   96 |     // Redimensionar a tamaño muy pequeño
   97 |     await page.setViewportSize({ width: 400, height: 300 });
   98 |     await page.waitForTimeout(1000);
   99 |     
  100 |     // Verificar que la aplicación sigue funcionando
  101 |     const canvasInfo = await helper.getCanvasInfo(page);
  102 |     expect(canvasInfo).toBeTruthy();
  103 |     expect(canvasInfo.canvas.width).toBeGreaterThan(0);
  104 |     
  105 |     // Redimensionar a tamaño muy grande
  106 |     await page.setViewportSize({ width: 2000, height: 1500 });
  107 |     await page.waitForTimeout(1000);
  108 |     
  109 |     // Verificar que sigue funcionando
  110 |     const canvasInfoLarge = await helper.getCanvasInfo(page);
  111 |     expect(canvasInfoLarge).toBeTruthy();
  112 |     expect(canvasInfoLarge.canvas.width).toBeGreaterThan(canvasInfo.canvas.width);
  113 |   });
  114 |
  115 |   test('debería manejar scroll extremo sin errores', async () => {
  116 |     await helper.selectTestRepo(page);
  117 |     
  118 |     // Scroll a posición negativa (no debería ser posible pero por si acaso)
  119 |     await page.evaluate(() => {
  120 |       const commitList = document.getElementById('commit-list');
  121 |       if (commitList) {
  122 |         commitList.scrollTop = -100;
  123 |       }
  124 |     });
  125 |     
  126 |     await page.waitForTimeout(500);
  127 |     
  128 |     // Verificar que el scroll se corrigió a 0
  129 |     const scrollTop = await page.evaluate(() => {
  130 |       const commitList = document.getElementById('commit-list');
  131 |       return commitList ? commitList.scrollTop : 0;
  132 |     });
  133 |     
  134 |     expect(scrollTop).toBeGreaterThanOrEqual(0);
  135 |     
  136 |     // Scroll a posición muy grande
  137 |     await page.evaluate(() => {
  138 |       const commitList = document.getElementById('commit-list');
  139 |       if (commitList) {
  140 |         commitList.scrollTop = 999999;
  141 |       }
  142 |     });
  143 |     
  144 |     await page.waitForTimeout(500);
  145 |     
  146 |     // Debería estar en el máximo posible
  147 |     const maxScrollTop = await page.evaluate(() => {
  148 |       const commitList = document.getElementById('commit-list');
  149 |       if (!commitList) return 0;
  150 |       return commitList.scrollHeight - commitList.clientHeight;
  151 |     });
  152 |     
  153 |     const finalScrollTop = await page.evaluate(() => {
  154 |       const commitList = document.getElementById('commit-list');
  155 |       return commitList ? commitList.scrollTop : 0;
  156 |     });
  157 |     
  158 |     expect(finalScrollTop).toBeLessThanOrEqual(maxScrollTop + 10); // Tolerancia
  159 |   });
  160 |
  161 |   test('debería manejar múltiples clicks rápidos en dots', async () => {
  162 |     await helper.selectTestRepo(page);
  163 |     
  164 |     // Hacer múltiples clicks rápidos en el mismo dot
  165 |     for (let i = 0; i < 10; i++) {
  166 |       await helper.clickCanvas(page, 16, 30);
  167 |       await page.waitForTimeout(50);
  168 |     }
  169 |     
  170 |     // Verificar que la aplicación sigue estable
```