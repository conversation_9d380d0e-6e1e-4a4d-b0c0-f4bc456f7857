# Test info

- Name: <PERSON><PERSON><PERSON> - Tests de Escenarios de Error >> debería manejar pérdida de conexión con el proceso principal
- Location: /Users/<USER>/repositorios/gitator/tests/error-scenarios.test.js:207:3

# Error details

```
Error: Timed out 10000ms waiting for expect(locator).toBeVisible()

Locator: locator('#error')
Expected: visible
Received: hidden
Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('#error')
    14 × locator resolved to <div id="error" class="error hidden">…</div>
       - unexpected value "hidden"

    at /Users/<USER>/repositorios/gitator/tests/error-scenarios.test.js:223:42
```

# Test source

```ts
  123 |       }
  124 |     });
  125 |     
  126 |     await page.waitForTimeout(500);
  127 |     
  128 |     // Verificar que el scroll se corrigió a 0
  129 |     const scrollTop = await page.evaluate(() => {
  130 |       const commitList = document.getElementById('commit-list');
  131 |       return commitList ? commitList.scrollTop : 0;
  132 |     });
  133 |     
  134 |     expect(scrollTop).toBeGreaterThanOrEqual(0);
  135 |     
  136 |     // Scroll a posición muy grande
  137 |     await page.evaluate(() => {
  138 |       const commitList = document.getElementById('commit-list');
  139 |       if (commitList) {
  140 |         commitList.scrollTop = 999999;
  141 |       }
  142 |     });
  143 |     
  144 |     await page.waitForTimeout(500);
  145 |     
  146 |     // Debería estar en el máximo posible
  147 |     const maxScrollTop = await page.evaluate(() => {
  148 |       const commitList = document.getElementById('commit-list');
  149 |       if (!commitList) return 0;
  150 |       return commitList.scrollHeight - commitList.clientHeight;
  151 |     });
  152 |     
  153 |     const finalScrollTop = await page.evaluate(() => {
  154 |       const commitList = document.getElementById('commit-list');
  155 |       return commitList ? commitList.scrollTop : 0;
  156 |     });
  157 |     
  158 |     expect(finalScrollTop).toBeLessThanOrEqual(maxScrollTop + 10); // Tolerancia
  159 |   });
  160 |
  161 |   test('debería manejar múltiples clicks rápidos en dots', async () => {
  162 |     await helper.selectTestRepo(page);
  163 |     
  164 |     // Hacer múltiples clicks rápidos en el mismo dot
  165 |     for (let i = 0; i < 10; i++) {
  166 |       await helper.clickCanvas(page, 16, 30);
  167 |       await page.waitForTimeout(50);
  168 |     }
  169 |     
  170 |     // Verificar que la aplicación sigue estable
  171 |     const commitsInfo = await helper.getCommitsInfo(page);
  172 |     expect(commitsInfo.count).toBeGreaterThan(0);
  173 |     
  174 |     // Debería haber exactamente un commit seleccionado
  175 |     const selectedCommits = commitsInfo.commits.filter(c => c.selected);
  176 |     expect(selectedCommits.length).toBeLessThanOrEqual(1);
  177 |     
  178 |     // El tooltip debería estar visible
  179 |     const tooltipInfo = await helper.getTooltipInfo(page);
  180 |     expect(tooltipInfo.visible).toBe(true);
  181 |   });
  182 |
  183 |   test('debería manejar cambios rápidos de opciones', async () => {
  184 |     await helper.selectTestRepo(page);
  185 |     
  186 |     // Cambiar opciones rápidamente
  187 |     for (let i = 0; i < 5; i++) {
  188 |       await page.check('#first-parent-only');
  189 |       await page.waitForTimeout(100);
  190 |       await page.uncheck('#first-parent-only');
  191 |       await page.waitForTimeout(100);
  192 |       await page.uncheck('#show-all-branches');
  193 |       await page.waitForTimeout(100);
  194 |       await page.check('#show-all-branches');
  195 |       await page.waitForTimeout(100);
  196 |     }
  197 |     
  198 |     // Verificar que la aplicación sigue funcionando
  199 |     const commitsInfo = await helper.getCommitsInfo(page);
  200 |     expect(commitsInfo.count).toBeGreaterThan(0);
  201 |     
  202 |     // Verificar que el canvas sigue renderizando
  203 |     const canvasInfo = await helper.getCanvasInfo(page);
  204 |     expect(canvasInfo.canvas.height).toBeGreaterThan(0);
  205 |   });
  206 |
  207 |   test('debería manejar pérdida de conexión con el proceso principal', async () => {
  208 |     await helper.selectTestRepo(page);
  209 |     
  210 |     // Mock de error en la API de Electron
  211 |     await page.evaluate(() => {
  212 |       const originalQueryCommits = window.electronAPI.queryCommits;
  213 |       window.electronAPI.queryCommits = async () => {
  214 |         throw new Error('IPC connection lost');
  215 |       };
  216 |     });
  217 |     
  218 |     // Intentar refresh
  219 |     await page.click('#refresh-btn');
  220 |     await page.waitForTimeout(2000);
  221 |     
  222 |     // Debería mostrar error
> 223 |     await expect(page.locator('#error')).toBeVisible();
      |                                          ^ Error: Timed out 10000ms waiting for expect(locator).toBeVisible()
  224 |     
  225 |     const errorMessage = await page.locator('#error-message').textContent();
  226 |     expect(errorMessage).toContain('Error al cargar commits');
  227 |   });
  228 |
  229 |   test('debería manejar commits con datos malformados', async () => {
  230 |     // Mock de commits con datos extraños
  231 |     await page.evaluate(() => {
  232 |       window.electronAPI.selectDirectory = async () => ({
  233 |         path: '/tmp/weird-git-repo',
  234 |         name: 'weird-git-repo',
  235 |         isGitRepository: true
  236 |       });
  237 |       
  238 |       window.electronAPI.queryCommits = async () => ({
  239 |         success: true,
  240 |         commits: [
  241 |           {
  242 |             sha: 'invalid-sha',
  243 |             subject: '',
  244 |             author: { name: '', email: '' },
  245 |             authorTimeShortStr: '',
  246 |             parents: [],
  247 |             decorators: [],
  248 |             hasDecorators: false,
  249 |             isCurrentHead: false,
  250 |             isMerged: false
  251 |           },
  252 |           {
  253 |             sha: 'a'.repeat(40),
  254 |             subject: 'x'.repeat(1000), // Subject muy largo
  255 |             author: { name: 'Test Author', email: '<EMAIL>' },
  256 |             authorTimeShortStr: '1 minute ago',
  257 |             parents: ['invalid-parent'],
  258 |             decorators: [],
  259 |             hasDecorators: false,
  260 |             isCurrentHead: false,
  261 |             isMerged: false
  262 |           }
  263 |         ],
  264 |         error: null
  265 |       });
  266 |     });
  267 |
  268 |     await page.click('#select-repo-btn');
  269 |     await page.waitForTimeout(1000);
  270 |
  271 |     // Debería cargar sin errores
  272 |     await expect(page.locator('#graph-container')).toBeVisible();
  273 |     
  274 |     const commitsInfo = await helper.getCommitsInfo(page);
  275 |     expect(commitsInfo.count).toBe(2);
  276 |     
  277 |     // Verificar que los commits se muestran (aunque con datos extraños)
  278 |     const commitElements = await page.locator('#commit-list .commit-item').count();
  279 |     expect(commitElements).toBe(2);
  280 |   });
  281 |
  282 |   test('debería manejar canvas sin contexto 2D', async () => {
  283 |     await helper.selectTestRepo(page);
  284 |     
  285 |     // Simular pérdida del contexto del canvas
  286 |     await page.evaluate(() => {
  287 |       const canvas = document.getElementById('commit-graph');
  288 |       if (canvas) {
  289 |         // Mock getContext para que retorne null
  290 |         const originalGetContext = canvas.getContext;
  291 |         canvas.getContext = () => null;
  292 |       }
  293 |     });
  294 |     
  295 |     // Intentar refresh para forzar re-renderizado
  296 |     await page.click('#refresh-btn');
  297 |     await page.waitForTimeout(1000);
  298 |     
  299 |     // La aplicación no debería crashear
  300 |     const isVisible = await page.locator('#graph-container').isVisible();
  301 |     expect(isVisible).toBe(true);
  302 |   });
  303 | });
  304 |
```