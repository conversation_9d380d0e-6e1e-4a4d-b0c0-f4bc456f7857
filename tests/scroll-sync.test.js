// Tests de sincronización de scroll entre grafo y lista de commits
const { test, expect, ElectronTestHelper } = require('./setup');

test.describe('Scroll Synchronization - Tests de Sincronización', () => {
  let helper;
  let electronApp;
  let page;
  let repoLoaded = false;

  // Configurar una sola vez para todos los tests
  test.beforeAll(async () => {
    helper = new ElectronTestHelper();
    const result = await helper.launch();
    electronApp = result.electronApp;
    page = result.page;

    // Cargar repositorio una sola vez para todos los tests
    await helper.selectTestRepo(page);
    await helper.waitForRepoLoad(page);
    repoLoaded = true;
    console.log('📚 Repositorio cargado para todos los tests de scroll');

    // Esperar a que haya suficientes commits para hacer scroll
    const commitsInfo = await helper.getCommitsInfo(page);
    if (commitsInfo.count < 10) {
      // Si no hay suficientes commits, cargar más
      await page.evaluate(() => {
        const commitList = document.getElementById('commit-list');
        if (commitList) {
          // Simular scroll al final para activar lazy loading
          commitList.scrollTop = commitList.scrollHeight;
          commitList.dispatchEvent(new Event('scroll'));
        }
      });
      await page.waitForTimeout(2000); // Esperar lazy loading
    }
  });

  test.afterAll(async () => {
    await helper.close();
  });

  // Verificar que el repositorio esté cargado antes de cada test
  test.beforeEach(async () => {
    if (!repoLoaded) {
      throw new Error('Repositorio no está cargado');
    }

    // Resetear scroll a posición inicial
    await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');
      if (commitList) commitList.scrollTop = 0;
      if (graphContainer) graphContainer.scrollTop = 0;
    });
  });

  test('scroll en lista de commits debería sincronizar con grafo', async () => {
    // Hacer scroll en la lista de commits
    const scrollAmount = 200;
    await helper.scrollCommitList(page, scrollAmount);
    await page.waitForTimeout(500); // Esperar sincronización

    // Verificar que el contenedor del grafo también hizo scroll
    const graphScrollTop = await page.evaluate(() => {
      const graphContainer = document.getElementById('commit-graph-container');
      return graphContainer ? graphContainer.scrollTop : 0;
    });

    expect(Math.abs(graphScrollTop - scrollAmount)).toBeLessThan(10); // Tolerancia de 10px
  });

  test('scroll en grafo debería sincronizar con lista de commits', async () => {
    // Hacer scroll en el contenedor del grafo
    const scrollAmount = 150;
    await page.evaluate((scrollAmount) => {
      const graphContainer = document.getElementById('commit-graph-container');
      if (graphContainer) {
        graphContainer.scrollTop = scrollAmount;
        graphContainer.dispatchEvent(new Event('scroll'));
      }
    }, scrollAmount);

    await page.waitForTimeout(500); // Esperar sincronización

    // Verificar que la lista de commits también hizo scroll
    const commitsInfo = await helper.getCommitsInfo(page);
    expect(Math.abs(commitsInfo.scrollTop - scrollAmount)).toBeLessThan(10); // Tolerancia de 10px
  });

  test('sincronización no debería crear bucle infinito', async () => {
    // Hacer múltiples scrolls rápidos para probar que no hay bucle infinito
    const scrollAmounts = [50, 100, 150, 200, 100, 50];

    for (const scrollAmount of scrollAmounts) {
      await helper.scrollCommitList(page, scrollAmount);
      await page.waitForTimeout(100); // Pequeña pausa entre scrolls
    }

    // Esperar a que se estabilice
    await page.waitForTimeout(1000);

    // Verificar que ambos contenedores están sincronizados
    const finalScrollPositions = await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');

      return {
        commitList: commitList ? commitList.scrollTop : 0,
        graphContainer: graphContainer ? graphContainer.scrollTop : 0
      };
    });

    expect(Math.abs(finalScrollPositions.commitList - finalScrollPositions.graphContainer)).toBeLessThan(10);
  });

  test('scroll debería mantener alineación visual entre dots y commits', async () => {
    // Hacer scroll a una posición específica
    const scrollAmount = 300;
    await helper.scrollCommitList(page, scrollAmount);
    await page.waitForTimeout(500);

    // Verificar que los dots siguen alineados con sus commits correspondientes
    const alignmentCheck = await page.evaluate(() => {
      const commitItems = document.querySelectorAll('#commit-list .commit-item');
      const canvas = document.getElementById('commit-graph');
      const commitList = document.getElementById('commit-list');

      if (!canvas || !commitList || commitItems.length === 0) return false;

      // Verificar algunos commits visibles
      const listRect = commitList.getBoundingClientRect();
      const canvasRect = canvas.getBoundingClientRect();

      let alignedCount = 0;
      let totalVisible = 0;

      commitItems.forEach((item, index) => {
        const itemRect = item.getBoundingClientRect();

        // Solo verificar items visibles
        if (itemRect.top >= listRect.top && itemRect.bottom <= listRect.bottom) {
          totalVisible++;

          // Calcular posición esperada del dot
          const expectedDotY = (index * 60) + 30; // Centro de la fila
          const canvasScrollTop = document.getElementById('commit-graph-container').scrollTop;
          const visibleDotY = expectedDotY - canvasScrollTop;

          // Verificar que el dot está en la posición correcta relativa al canvas visible
          if (visibleDotY >= 0 && visibleDotY <= canvasRect.height) {
            alignedCount++;
          }
        }
      });

      return { alignedCount, totalVisible, ratio: totalVisible > 0 ? alignedCount / totalVisible : 0 };
    });

    // Al menos el 80% de los dots visibles deberían estar alineados
    expect(alignmentCheck.ratio).toBeGreaterThan(0.8);
  });

  test('scroll suave al seleccionar commit debería funcionar', async () => {
    // Hacer scroll al inicio
    await helper.scrollCommitList(page, 0);
    await page.waitForTimeout(500);

    // Obtener un commit que esté fuera de la vista
    const commitsInfo = await helper.getCommitsInfo(page);
    const targetCommitIndex = Math.min(10, commitsInfo.count - 1); // Commit 10 o el último
    const targetCommit = commitsInfo.commits[targetCommitIndex];

    // Simular selección de commit (esto debería activar scroll automático)
    await page.evaluate((sha) => {
      // Simular click en commit
      const commitEl = document.querySelector(`[data-sha="${sha}"]`);
      if (commitEl) {
        commitEl.click();
      }
    }, targetCommit.sha);

    await page.waitForTimeout(1000); // Esperar scroll suave

    // Verificar que el commit seleccionado está visible
    const updatedCommitsInfo = await helper.getCommitsInfo(page);
    const selectedCommit = updatedCommitsInfo.commits.find(c => c.selected);

    expect(selectedCommit).toBeTruthy();
    expect(selectedCommit.sha).toBe(targetCommit.sha);

    // Verificar que el commit está en el viewport
    const isVisible = await page.evaluate((sha) => {
      const commitEl = document.querySelector(`[data-sha="${sha}"]`);
      const commitList = document.getElementById('commit-list');

      if (!commitEl || !commitList) return false;

      const listRect = commitList.getBoundingClientRect();
      const commitRect = commitEl.getBoundingClientRect();

      return (
        commitRect.top >= listRect.top &&
        commitRect.bottom <= listRect.bottom
      );
    }, targetCommit.sha);

    expect(isVisible).toBe(true);
  });

  test('scroll debería mantener sincronización durante redimensionado', async () => {
    // Hacer scroll inicial
    const initialScroll = 100;
    await helper.scrollCommitList(page, initialScroll);
    await page.waitForTimeout(500);

    // Simular redimensionado de ventana
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000); // Esperar redimensionado y re-renderizado

    // Verificar que la sincronización se mantiene
    const scrollPositions = await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');

      return {
        commitList: commitList ? commitList.scrollTop : 0,
        graphContainer: graphContainer ? graphContainer.scrollTop : 0
      };
    });

    expect(Math.abs(scrollPositions.commitList - scrollPositions.graphContainer)).toBeLessThan(20);
  });

  test('scroll rápido no debería causar problemas de rendimiento', async () => {
    const startTime = Date.now();

    // Hacer muchos scrolls rápidos
    for (let i = 0; i < 20; i++) {
      const scrollAmount = (i * 50) % 500; // Scroll entre 0 y 500
      await helper.scrollCommitList(page, scrollAmount);
      await page.waitForTimeout(50); // Scroll muy rápido
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    // No debería tomar más de 5 segundos
    expect(duration).toBeLessThan(5000);

    // Verificar que la aplicación sigue respondiendo
    const isResponsive = await page.evaluate(() => {
      // Verificar que los elementos principales siguen siendo accesibles
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');
      const canvas = document.getElementById('commit-graph');

      return !!(commitList && graphContainer && canvas);
    });

    expect(isResponsive).toBe(true);
  });
});
