// Tests instrumentados para selección de commits y scroll
const { test, expect, ElectronTestHelper } = require('./setup');

test.describe('Commit Selection - Tests de Selección de Commits', () => {
  let helper;
  let electronApp;
  let page;
  let repoLoaded = false;

  // Configurar una sola vez para todos los tests
  test.beforeAll(async () => {
    helper = new ElectronTestHelper();
    const result = await helper.launch();
    electronApp = result.electronApp;
    page = result.page;

    // Seleccionar repositorio una sola vez para todos los tests
    await helper.selectTestRepo(page);
    await helper.waitForRepoLoad(page);
    repoLoaded = true;
    console.log('📚 Repositorio cargado para todos los tests de selección');
  });

  test.afterAll(async () => {
    await helper.close();
  });

  // Verificar que el repositorio esté cargado antes de cada test
  test.beforeEach(async () => {
    if (!repoLoaded) {
      throw new Error('Repositorio no está cargado');
    }

    // Limpiar cualquier selección anterior
    await page.evaluate(() => {
      document.querySelectorAll('.commit-item.selected').forEach(el => {
        el.classList.remove('selected');
      });
    });
  });

  test('seleccionar un punto de commit debería seleccionar su información correspondiente', async () => {
    console.log('🎯 Test: Selección de commit por click en punto del grafo');

    // Verificar que hay commits cargados
    const commitsInfo = await helper.getCommitsInfo(page);
    expect(commitsInfo.count).toBeGreaterThan(0);
    console.log(`📊 Commits cargados: ${commitsInfo.count}`);

    // Obtener información del canvas y commits
    const canvasInfo = await helper.getCanvasInfo(page);
    expect(canvasInfo.visible).toBe(true);
    console.log(`🎨 Canvas visible: ${canvasInfo.width}x${canvasInfo.height}`);

    // Obtener posiciones de los dots de commits
    const commitDots = await page.evaluate(() => {
      const app = window.gitatorApp;
      if (!app || !app.commitDots) return [];

      return app.commitDots.slice(0, 5).map(dot => ({
        hash: dot.hash,
        x: dot.x,
        y: dot.y,
        radius: dot.radius,
        index: dot.index,
        commitSubject: dot.commit.subject
      }));
    });

    expect(commitDots.length).toBeGreaterThan(0);
    console.log(`🔵 Dots disponibles: ${commitDots.length}`);

    // Seleccionar el primer commit dot
    const firstDot = commitDots[0];
    console.log(`🎯 Seleccionando commit: ${firstDot.hash.substring(0, 8)} - "${firstDot.commitSubject}"`);
    console.log(`📍 Posición del dot: (${firstDot.x}, ${firstDot.y})`);

    // Click en el dot del commit
    await helper.clickCanvas(page, firstDot.x, firstDot.y);
    await page.waitForTimeout(500); // Esperar a que se procese la selección

    // Verificar que el commit se seleccionó correctamente
    const selectedCommitInfo = await page.evaluate((expectedHash) => {
      // Verificar que el elemento del commit tiene la clase 'selected'
      const commitElement = document.querySelector(`[data-sha="${expectedHash}"]`);
      if (!commitElement) return { found: false };

      const isSelected = commitElement.classList.contains('selected');
      const commitSubject = commitElement.querySelector('.commit-subject')?.textContent;
      const commitSha = commitElement.querySelector('.commit-sha')?.textContent;

      return {
        found: true,
        isSelected,
        subject: commitSubject,
        sha: commitSha,
        elementVisible: commitElement.offsetParent !== null
      };
    }, firstDot.hash);

    expect(selectedCommitInfo.found).toBe(true);
    expect(selectedCommitInfo.isSelected).toBe(true);
    console.log(`✅ Commit seleccionado correctamente: ${selectedCommitInfo.sha} - "${selectedCommitInfo.subject}"`);

    // Verificar que el tooltip se muestra (si el commit tiene dot)
    const tooltipInfo = await helper.getTooltipInfo(page);
    if (tooltipInfo) {
      expect(tooltipInfo.visible).toBe(true);
      // El tooltip puede mostrar 6 o más caracteres del hash
      expect(tooltipInfo.text).toContain(firstDot.hash.substring(0, 6));
      console.log(`💬 Tooltip visible: "${tooltipInfo.text}"`);
    }

    // Verificar que solo hay un commit seleccionado
    const allSelectedCommits = await page.evaluate(() => {
      const selectedElements = document.querySelectorAll('.commit-item.selected');
      return selectedElements.length;
    });

    expect(allSelectedCommits).toBe(1);
    console.log(`🎯 Solo un commit seleccionado: ${allSelectedCommits}`);

    // Probar seleccionar otro commit
    if (commitDots.length > 1) {
      const secondDot = commitDots[1];
      console.log(`🎯 Seleccionando segundo commit: ${secondDot.hash.substring(0, 8)}`);

      await helper.clickCanvas(page, secondDot.x, secondDot.y);
      await page.waitForTimeout(500);

      // Verificar que la selección cambió
      const newSelectedCommitInfo = await page.evaluate((expectedHash) => {
        const commitElement = document.querySelector(`[data-sha="${expectedHash}"]`);
        return {
          found: !!commitElement,
          isSelected: commitElement?.classList.contains('selected') || false
        };
      }, secondDot.hash);

      expect(newSelectedCommitInfo.found).toBe(true);
      expect(newSelectedCommitInfo.isSelected).toBe(true);

      // Verificar que el primer commit ya no está seleccionado
      const firstCommitStillSelected = await page.evaluate((expectedHash) => {
        const commitElement = document.querySelector(`[data-sha="${expectedHash}"]`);
        return commitElement?.classList.contains('selected') || false;
      }, firstDot.hash);

      expect(firstCommitStillSelected).toBe(false);
      console.log(`✅ Selección cambió correctamente al segundo commit`);
    }
  });

  test('scroll y selección de commit - auto scroll a commit seleccionado', async () => {
    console.log('🎯 Test: Scroll y auto-scroll a commit seleccionado');

    // Verificar que hay commits cargados
    const commitsInfo = await helper.getCommitsInfo(page);
    expect(commitsInfo.count).toBeGreaterThan(10); // Necesitamos suficientes commits para scroll
    console.log(`📊 Commits cargados: ${commitsInfo.count}`);

    // Obtener información inicial del scroll
    const initialScrollInfo = await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');

      return {
        commitListScrollTop: commitList?.scrollTop || 0,
        graphScrollTop: graphContainer?.scrollTop || 0,
        commitListHeight: commitList?.clientHeight || 0,
        commitListScrollHeight: commitList?.scrollHeight || 0
      };
    });

    console.log(`📜 Scroll inicial - Lista: ${initialScrollInfo.commitListScrollTop}, Grafo: ${initialScrollInfo.graphScrollTop}`);
    console.log(`📏 Dimensiones lista - Altura: ${initialScrollInfo.commitListHeight}, Scroll total: ${initialScrollInfo.commitListScrollHeight}`);

    // Hacer scroll hacia abajo para que algunos commits no sean visibles
    const scrollAmount = Math.min(500, initialScrollInfo.commitListScrollHeight / 3);
    console.log(`⬇️ Haciendo scroll hacia abajo: ${scrollAmount}px`);

    await helper.scrollCommitList(page, scrollAmount);
    await page.waitForTimeout(500); // Esperar a que se complete el scroll

    // Verificar que el scroll se aplicó
    const afterScrollInfo = await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const graphContainer = document.getElementById('commit-graph-container');

      return {
        commitListScrollTop: commitList?.scrollTop || 0,
        graphScrollTop: graphContainer?.scrollTop || 0
      };
    });

    expect(afterScrollInfo.commitListScrollTop).toBeGreaterThan(initialScrollInfo.commitListScrollTop);
    console.log(`📜 Scroll después - Lista: ${afterScrollInfo.commitListScrollTop}, Grafo: ${afterScrollInfo.graphScrollTop}`);

    // Verificar sincronización de scroll entre lista y grafo
    expect(Math.abs(afterScrollInfo.commitListScrollTop - afterScrollInfo.graphScrollTop)).toBeLessThan(5);
    console.log(`🔄 Scroll sincronizado correctamente entre lista y grafo`);

    // Obtener un commit que esté fuera del viewport actual (arriba)
    const commitOutOfView = await page.evaluate(() => {
      const commitList = document.getElementById('commit-list');
      const commitItems = document.querySelectorAll('.commit-item');

      if (!commitList || commitItems.length === 0) return null;

      const listRect = commitList.getBoundingClientRect();

      // Buscar un commit que esté arriba del viewport
      for (let i = 0; i < Math.min(5, commitItems.length); i++) {
        const commitEl = commitItems[i];
        const commitRect = commitEl.getBoundingClientRect();

        // Si el commit está arriba del viewport visible
        if (commitRect.bottom < listRect.top) {
          return {
            hash: commitEl.dataset.sha,
            index: i,
            subject: commitEl.querySelector('.commit-subject')?.textContent,
            offsetTop: commitEl.offsetTop
          };
        }
      }

      // Si no hay commits arriba, usar el primer commit
      const firstCommit = commitItems[0];
      return {
        hash: firstCommit.dataset.sha,
        index: 0,
        subject: firstCommit.querySelector('.commit-subject')?.textContent,
        offsetTop: firstCommit.offsetTop
      };
    });

    expect(commitOutOfView).toBeTruthy();
    console.log(`🎯 Commit fuera de vista: ${commitOutOfView.hash.substring(0, 8)} - "${commitOutOfView.subject}"`);

    // Obtener la posición del dot correspondiente a este commit
    const targetDot = await page.evaluate((targetHash) => {
      const app = window.gitatorApp;
      if (!app || !app.commitDots) return null;

      const dot = app.commitDots.find(d => d.hash === targetHash);
      return dot ? {
        x: dot.x,
        y: dot.y,
        hash: dot.hash,
        index: dot.index
      } : null;
    }, commitOutOfView.hash);

    if (targetDot) {
      console.log(`🔵 Dot encontrado en posición: (${targetDot.x}, ${targetDot.y})`);

      // Click en el dot del commit que está fuera de vista
      await helper.clickCanvas(page, targetDot.x, targetDot.y);
      await page.waitForTimeout(1000); // Esperar a que se complete el auto-scroll

      // Verificar que el commit se seleccionó
      const isSelected = await page.evaluate((targetHash) => {
        const commitElement = document.querySelector(`[data-sha="${targetHash}"]`);
        return commitElement?.classList.contains('selected') || false;
      }, commitOutOfView.hash);

      expect(isSelected).toBe(true);
      console.log(`✅ Commit seleccionado correctamente`);

      // Verificar que se hizo auto-scroll para mostrar el commit seleccionado
      const finalScrollInfo = await page.evaluate((targetHash) => {
        const commitList = document.getElementById('commit-list');
        const commitElement = document.querySelector(`[data-sha="${targetHash}"]`);

        if (!commitList || !commitElement) return null;

        const listRect = commitList.getBoundingClientRect();
        const commitRect = commitElement.getBoundingClientRect();

        const isVisible = (
          commitRect.top >= listRect.top &&
          commitRect.bottom <= listRect.bottom
        );

        return {
          commitListScrollTop: commitList.scrollTop,
          isCommitVisible: isVisible,
          commitTop: commitRect.top,
          commitBottom: commitRect.bottom,
          listTop: listRect.top,
          listBottom: listRect.bottom
        };
      }, commitOutOfView.hash);

      expect(finalScrollInfo).toBeTruthy();
      console.log(`📜 Scroll final: ${finalScrollInfo.commitListScrollTop}`);
      console.log(`👁️ Commit visible: ${finalScrollInfo.isCommitVisible}`);

      // El commit debería estar visible después del auto-scroll
      expect(finalScrollInfo.isCommitVisible).toBe(true);
      console.log(`✅ Auto-scroll funcionó correctamente - commit ahora visible`);

      // Verificar que el scroll cambió desde la posición anterior
      expect(finalScrollInfo.commitListScrollTop).not.toBe(afterScrollInfo.commitListScrollTop);
      console.log(`🔄 Scroll se ajustó automáticamente`);

    } else {
      console.log(`⚠️ No se encontró dot para el commit ${commitOutOfView.hash.substring(0, 8)}`);

      // Test alternativo: seleccionar un commit visible y verificar que no hay auto-scroll innecesario
      const visibleCommitDots = await page.evaluate(() => {
        const app = window.gitatorApp;
        if (!app || !app.commitDots) return [];

        return app.commitDots.slice(0, 3).map(dot => ({
          hash: dot.hash,
          x: dot.x,
          y: dot.y,
          index: dot.index
        }));
      });

      if (visibleCommitDots.length > 0) {
        const visibleDot = visibleCommitDots[0];
        console.log(`🎯 Seleccionando commit visible: ${visibleDot.hash.substring(0, 8)}`);

        const scrollBeforeClick = afterScrollInfo.commitListScrollTop;

        await helper.clickCanvas(page, visibleDot.x, visibleDot.y);
        await page.waitForTimeout(500);

        const scrollAfterClick = await page.evaluate(() => {
          const commitList = document.getElementById('commit-list');
          return commitList?.scrollTop || 0;
        });

        // Para un commit ya visible, el scroll no debería cambiar mucho
        const scrollDifference = Math.abs(scrollAfterClick - scrollBeforeClick);
        console.log(`📏 Diferencia de scroll: ${scrollDifference}px`);

        // Permitir pequeñas diferencias debido a la sincronización
        expect(scrollDifference).toBeLessThan(50);
        console.log(`✅ No hubo auto-scroll innecesario para commit ya visible`);
      }
    }
  });
});
