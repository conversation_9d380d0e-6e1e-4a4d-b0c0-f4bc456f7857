# Tests de Gitator

Este directorio contiene tests instrumentados para la aplicación Gitator usando Playwright for Electron.

## Estructura de Tests

### 📁 Archivos de Test

- **`app.test.js`** - Tests básicos de la aplicación
  - Carga inicial de la aplicación
  - Selección de repositorios
  - Controles de ventana
  - Estados de error

- **`git-graph.test.js`** - Tests del visualizador de grafo
  - Renderizado del canvas
  - Alineación de dots con commits
  - Detección de clicks
  - Tooltips de hash
  - Decoradores de commits

- **`scroll-sync.test.js`** - Tests de sincronización de scroll
  - Sincronización bidireccional
  - Prevención de bucles infinitos
  - Scroll suave automático
  - Rendimiento de scroll

- **`commit-selection.test.js`** - Tests de selección de commits
  - Selección por click en puntos del grafo
  - Correspondencia entre puntos y información de commits
  - Auto-scroll a commits seleccionados
  - Persistencia de selección durante scroll
  - Sincronización de scroll entre grafo y lista

- **`lazy-loading.test.js`** - Tests de carga progresiva
  - Carga inicial de commits
  - Activación de lazy loading
  - Indicadores de carga
  - Manejo del final del historial

- **`error-scenarios.test.js`** - Tests de escenarios de error
  - Repositorios vacíos o corruptos
  - Errores de conexión IPC
  - Datos malformados
  - Casos extremos de UI

### 🛠️ Archivos de Configuración

- **`setup.js`** - Helpers y configuración común
- **`run-tests.js`** - Script personalizado para ejecutar tests
- **`../playwright.config.js`** - Configuración de Playwright

## Errores Específicos que se Testean

### 🎯 Problemas de Alineación
- **Dots desalineados**: Verifica que los puntos del grafo estén alineados verticalmente con las filas de commits
- **Scroll desincronizado**: Asegura que el scroll entre grafo y lista se mantenga sincronizado

### 🔄 Problemas de Lazy Loading
- **Carga duplicada**: Previene múltiples cargas simultáneas
- **Pérdida de posición**: Mantiene la posición de scroll al cargar más commits
- **Indicadores incorrectos**: Verifica que los indicadores de carga aparezcan/desaparezcan correctamente

### 🖱️ Problemas de Interacción
- **Clicks perdidos**: Asegura que los clicks en dots seleccionen el commit correcto
- **Tooltips persistentes**: Verifica que los tooltips se oculten apropiadamente
- **Cursor incorrecto**: Comprueba que el cursor cambie sobre elementos interactivos

### 🎨 Problemas de Renderizado
- **Canvas vacío**: Detecta cuando el grafo no se renderiza
- **Redimensionado**: Verifica que el canvas se ajuste correctamente al redimensionar
- **Opciones de filtro**: Asegura que los cambios de opciones actualicen el grafo

## Comandos de Test

### Ejecutar todos los tests
```bash
npm test
```

### Tests específicos
```bash
# Tests básicos
node tests/run-tests.js basic

# Tests del grafo
node tests/run-tests.js graph

# Tests de scroll
node tests/run-tests.js scroll

# Tests de selección de commits
node tests/run-tests.js selection

# Tests de lazy loading
node tests/run-tests.js lazy

# Tests de errores
node tests/run-tests.js errors
```

### Modos especiales
```bash
# Modo visual (ver la aplicación ejecutándose)
npm run test:headed
node tests/run-tests.js headed

# Modo debug (pausar en breakpoints)
npm run test:debug
node tests/run-tests.js debug

# Interfaz de usuario de tests
npm run test:ui
node tests/run-tests.js ui

# Ver reporte de resultados
npm run test:report
```

## Helpers Disponibles

### `ElectronTestHelper`

Clase principal que proporciona métodos para:

- **`launch()`** - Iniciar la aplicación Electron
- **`close()`** - Cerrar la aplicación
- **`selectTestRepo(page, path)`** - Seleccionar un repositorio de test
- **`waitForRepoLoad(page)`** - Esperar a que se cargue un repositorio
- **`getCanvasInfo(page)`** - Obtener información del canvas
- **`getCommitsInfo(page)`** - Obtener información de commits
- **`scrollCommitList(page, scrollTop)`** - Hacer scroll en la lista
- **`clickCanvas(page, x, y)`** - Click en coordenadas del canvas
- **`getTooltipInfo(page)`** - Obtener estado del tooltip

## Configuración de CI/CD

Los tests están configurados para ejecutarse en:
- ✅ Modo headless por defecto
- ✅ Con retry automático en caso de fallo
- ✅ Generación de screenshots en fallos
- ✅ Reporte HTML detallado

## Troubleshooting

### Tests fallan al iniciar Electron
```bash
# Asegúrate de que la aplicación esté construida
npm run build
```

### Tests lentos o timeout
```bash
# Ejecutar con más tiempo
npx playwright test --timeout=60000
```

### Ver qué está pasando
```bash
# Modo headed para ver la aplicación
npm run test:headed

# Modo debug para pausar y inspeccionar
npm run test:debug
```

### Problemas de sincronización
Los tests incluyen esperas apropiadas, pero si hay problemas:
- Aumentar timeouts en `playwright.config.js`
- Usar `page.waitForTimeout()` adicionales
- Verificar que los selectores sean correctos

## Contribuir

Al agregar nuevos tests:

1. **Usar helpers existentes** cuando sea posible
2. **Incluir cleanup** en `afterEach`
3. **Esperar elementos** antes de interactuar
4. **Verificar estados** antes y después de acciones
5. **Documentar casos de error** específicos que se testean

## Métricas de Cobertura

Los tests cubren:
- ✅ Funcionalidad básica de la aplicación
- ✅ Visualización del grafo Git
- ✅ Sincronización de scroll
- ✅ Lazy loading de commits
- ✅ Manejo de errores
- ✅ Casos extremos de UI
- ✅ Interacciones de usuario
- ✅ Rendimiento básico
