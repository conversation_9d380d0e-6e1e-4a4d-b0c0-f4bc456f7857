// Setup para tests de Electron con Playwright
const { test, expect, _electron } = require('@playwright/test');
const path = require('path');

class ElectronTestHelper {
  constructor() {
    this.electronApp = null;
    this.page = null;
  }

  async launch() {
    // Lanzar Electron con ventana oculta/minimizada
    this.electronApp = await _electron.launch({
      args: ['.', '--no-sandbox', '--disable-dev-shm-usage'],
      timeout: 60000,
      env: {
        ...process.env,
        NODE_ENV: 'test',
        ELECTRON_DISABLE_SECURITY_WARNINGS: 'true',
        DISPLAY: ':99' // Display virtual para Linux/CI
      }
    });

    // Obtener la primera ventana
    this.page = await this.electronApp.firstWindow();
    await this.page.waitForLoadState('domcontentloaded');

    // Esperar a que la aplicación esté completamente cargada
    await this.page.waitForSelector('#app', { state: 'visible' });

    // Los mocks están en el proceso principal, no necesitamos configurar nada aquí

    return { electronApp: this.electronApp, page: this.page };
  }

  async close() {
    if (this.electronApp) {
      await this.electronApp.close();
    }
  }

  // Configurar mocks básicos
  async setupMocks(page) {
    await page.evaluate(() => {
      // Verificar que electronAPI existe
      if (typeof window.electronAPI === 'undefined') {
        console.log('electronAPI no existe, creando mock...');
        window.electronAPI = {};
      }

      // Mock por defecto de selectDirectory
      window.electronAPI.selectDirectory = async () => ({
        path: '/Users/<USER>/repositorios/vscode',
        name: 'vscode',
        isGitRepository: true
      });

      // Mock por defecto de queryCommits
      window.electronAPI.queryCommits = async () => ({
        success: true,
        commits: [
          {
            sha: 'abcdef1234567890abcdef1234567890abcdef12',
            subject: 'Initial commit',
            author: { name: 'Test Author', email: '<EMAIL>' },
            authorTimeShortStr: '1 day ago',
            parents: [],
            decorators: [{ type: 1, name: 'main' }],
            hasDecorators: true,
            isCurrentHead: true,
            isMerged: false
          },
          {
            sha: '1234567890abcdef1234567890abcdef12345678',
            subject: 'Add feature X',
            author: { name: 'Test Author', email: '<EMAIL>' },
            authorTimeShortStr: '2 days ago',
            parents: ['abcdef1234567890abcdef1234567890abcdef12'],
            decorators: [],
            hasDecorators: false,
            isCurrentHead: false,
            isMerged: false
          }
        ],
        error: null
      });

      console.log('Mocks configurados correctamente');
    });
  }

  // Helper para esperar a que el repositorio se cargue
  async waitForRepoLoad(page) {
    // Debug: verificar qué elementos están visibles
    await page.waitForTimeout(2000); // Esperar un poco

    const debugInfo = await page.evaluate(() => {
      const graphContainer = document.getElementById('graph-container');
      const noRepo = document.getElementById('no-repo');
      const error = document.getElementById('error');
      const loading = document.getElementById('loading');

      return {
        graphContainer: {
          exists: !!graphContainer,
          visible: graphContainer ? !graphContainer.classList.contains('hidden') : false,
          classes: graphContainer ? graphContainer.className : 'not found'
        },
        noRepo: {
          exists: !!noRepo,
          visible: noRepo ? !noRepo.classList.contains('hidden') : false,
          classes: noRepo ? noRepo.className : 'not found'
        },
        error: {
          exists: !!error,
          visible: error ? !error.classList.contains('hidden') : false,
          classes: error ? error.className : 'not found'
        },
        loading: {
          exists: !!loading,
          visible: loading ? !loading.classList.contains('hidden') : false,
          classes: loading ? loading.className : 'not found'
        }
      };
    });

    console.log('Debug info:', JSON.stringify(debugInfo, null, 2));

    // Intentar esperar por el graph-container (timeout largo para selección manual)
    try {
      await page.waitForSelector('#graph-container:not(.hidden)', { timeout: 30000 });
      await page.waitForSelector('#commit-list .commit-item', { timeout: 10000 });
      console.log('✅ Repositorio cargado exitosamente!');
    } catch (error) {
      console.log('Error esperando elementos:', error.message);
      throw error;
    }
  }

  // Helper para seleccionar un repositorio de test
  async selectTestRepo(page, repoPath = null) {
    // Configurar mock automático para evitar selección manual repetitiva
    await page.evaluate(() => {
      // Mock del electronAPI.selectDirectory para devolver automáticamente el repo de test
      if (window.electronAPI) {
        window.electronAPI.selectDirectory = async () => ({
          path: '/Users/<USER>/repositorios/vscode',
          name: 'vscode',
          isGitRepository: true
        });
      }
    });

    // Hacer click en el botón
    await page.click('#select-repo-btn');

    console.log('🔧 Usando repositorio de test automático: /Users/<USER>/repositorios/vscode');

    // Esperar a que se cargue el repositorio
    await this.waitForRepoLoad(page);
  }

  // Helper para obtener información del canvas
  async getCanvasInfo(page) {
    return await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      const container = document.getElementById('commit-graph-container');
      const graphContainer = document.getElementById('graph-container');

      if (!canvas || !container) {
        return {
          visible: false,
          canvas: null,
          container: null
        };
      }

      const rect = canvas.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      const isVisible = !graphContainer?.classList.contains('hidden') &&
                       rect.width > 0 && rect.height > 0;

      return {
        visible: isVisible,
        width: canvas.width,
        height: canvas.height,
        canvas: {
          width: canvas.width,
          height: canvas.height,
          styleWidth: canvas.style.width,
          styleHeight: canvas.style.height,
          rect: rect
        },
        container: {
          rect: containerRect,
          scrollTop: container.scrollTop,
          scrollHeight: container.scrollHeight
        }
      };
    });
  }

  // Helper para obtener información de commits
  async getCommitsInfo(page) {
    return await page.evaluate(() => {
      const commitItems = document.querySelectorAll('#commit-list .commit-item');
      const commitList = document.getElementById('commit-list');

      return {
        count: commitItems.length,
        scrollTop: commitList ? commitList.scrollTop : 0,
        scrollHeight: commitList ? commitList.scrollHeight : 0,
        commits: Array.from(commitItems).map((item, index) => ({
          index,
          sha: item.dataset.sha,
          subject: item.querySelector('.commit-subject')?.textContent,
          selected: item.classList.contains('selected'),
          rect: item.getBoundingClientRect()
        }))
      };
    });
  }

  // Helper para simular scroll
  async scrollCommitList(page, scrollTop) {
    await page.evaluate((scrollTop) => {
      const commitList = document.getElementById('commit-list');
      if (commitList) {
        commitList.scrollTop = scrollTop;
        commitList.dispatchEvent(new Event('scroll'));
      }
    }, scrollTop);
  }

  // Helper para simular click en canvas
  async clickCanvas(page, x, y) {
    // Método más robusto para hacer click en el canvas
    await page.evaluate((clickX, clickY) => {
      const canvas = document.getElementById('commit-graph');
      if (!canvas) return;

      // Crear evento de click sintético
      const rect = canvas.getBoundingClientRect();
      const event = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        clientX: rect.left + clickX,
        clientY: rect.top + clickY,
        button: 0
      });

      // Disparar el evento directamente en el canvas
      canvas.dispatchEvent(event);
    }, x, y);
  }

  // Helper para verificar tooltip
  async getTooltipInfo(page) {
    return await page.evaluate(() => {
      const tooltip = document.getElementById('commit-hash-tooltip');
      if (!tooltip) return null;

      return {
        visible: tooltip.classList.contains('visible'),
        text: tooltip.textContent,
        style: {
          left: tooltip.style.left,
          top: tooltip.style.top
        }
      };
    });
  }
}

module.exports = { ElectronTestHelper, test, expect };
