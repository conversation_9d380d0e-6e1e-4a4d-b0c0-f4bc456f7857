// Tests específicos del visualizador de grafo Git
const { test, expect, ElectronTestHelper } = require('./setup');

test.describe('Git Graph Visualization - Tests Específicos', () => {
  let helper;
  let electronApp;
  let page;
  let repoLoaded = false;

  // Configurar una sola vez para todos los tests
  test.beforeAll(async () => {
    helper = new ElectronTestHelper();
    const result = await helper.launch();
    electronApp = result.electronApp;
    page = result.page;

    // Cargar repositorio una sola vez para todos los tests
    await helper.selectTestRepo(page);
    await helper.waitForRepoLoad(page);
    repoLoaded = true;
    console.log('📚 Repositorio cargado para todos los tests del grafo');
  });

  test.afterAll(async () => {
    await helper.close();
  });

  // Verificar que el repositorio esté cargado antes de cada test
  test.beforeEach(async () => {
    if (!repoLoaded) {
      throw new Error('Repositorio no está cargado');
    }
  });

  test('canvas debería tener el tamaño correcto', async () => {
    const canvasInfo = await helper.getCanvasInfo(page);

    expect(canvasInfo).toBeTruthy();
    expect(canvasInfo.canvas.width).toBeGreaterThan(0);
    expect(canvasInfo.canvas.height).toBeGreaterThan(0);

    // El canvas debería tener altura proporcional al número de commits
    const commitsInfo = await helper.getCommitsInfo(page);
    const expectedHeight = commitsInfo.count * 60; // 60px por commit

    // Verificar que la altura del canvas es aproximadamente correcta
    const actualHeight = parseInt(canvasInfo.canvas.styleHeight);
    expect(Math.abs(actualHeight - expectedHeight)).toBeLessThan(100);
  });

  test('dots deberían estar alineados con las filas de commits', async () => {
    const commitsInfo = await helper.getCommitsInfo(page);
    expect(commitsInfo.count).toBeGreaterThan(0);

    // Obtener información de los dots del canvas
    const dotsInfo = await page.evaluate(() => {
      // Acceder a la instancia de la aplicación para obtener commitDots
      const app = window.gitatorApp; // Necesitamos exponer esto en el código principal
      if (!app || !app.commitDots) return [];

      return app.commitDots.map(dot => ({
        hash: dot.hash,
        x: dot.x,
        y: dot.y,
        index: dot.index
      }));
    });

    // Verificar que cada dot está alineado con su fila correspondiente
    for (const dot of dotsInfo) {
      const expectedY = (dot.index * 60) + 30; // Centro de la fila (60px altura, +30 para centro)
      expect(Math.abs(dot.y - expectedY)).toBeLessThan(5); // Tolerancia de 5px
    }
  });

  test('click en dot debería seleccionar commit correspondiente', async () => {
    const commitsInfo = await helper.getCommitsInfo(page);
    expect(commitsInfo.count).toBeGreaterThan(0);

    // Obtener el primer commit
    const firstCommit = commitsInfo.commits[0];

    // Calcular posición aproximada del dot (asumiendo que está en x=16, centro de la fila)
    const dotX = 16;
    const dotY = 30; // Centro de la primera fila

    // Click en el dot
    await helper.clickCanvas(page, dotX, dotY);

    // Verificar que el commit se seleccionó
    await page.waitForTimeout(500); // Esperar a que se procese el click

    const updatedCommitsInfo = await helper.getCommitsInfo(page);
    const selectedCommit = updatedCommitsInfo.commits.find(c => c.selected);

    expect(selectedCommit).toBeTruthy();
    expect(selectedCommit.sha).toBe(firstCommit.sha);
  });

  test('tooltip debería mostrarse al hacer click en dot', async () => {
    // Click en el primer dot
    const dotX = 16;
    const dotY = 30;

    await helper.clickCanvas(page, dotX, dotY);
    await page.waitForTimeout(500);

    // Verificar que el tooltip es visible
    const tooltipInfo = await helper.getTooltipInfo(page);

    expect(tooltipInfo).toBeTruthy();
    expect(tooltipInfo.visible).toBe(true);
    expect(tooltipInfo.text).toMatch(/^[a-f0-9]{6}$/); // 6 caracteres hexadecimales
  });

  test('tooltip debería ocultarse al hacer scroll', async () => {
    // Mostrar tooltip primero
    await helper.clickCanvas(page, 16, 30);
    await page.waitForTimeout(500);

    let tooltipInfo = await helper.getTooltipInfo(page);
    expect(tooltipInfo.visible).toBe(true);

    // Hacer scroll
    await helper.scrollCommitList(page, 100);
    await page.waitForTimeout(500);

    // Verificar que el tooltip se ocultó
    tooltipInfo = await helper.getTooltipInfo(page);
    expect(tooltipInfo.visible).toBe(false);
  });

  test('cursor debería cambiar al pasar sobre dots', async () => {
    // Mover mouse sobre un dot
    const canvas = page.locator('#commit-graph');
    await canvas.hover({ position: { x: 16, y: 30 } });
    await page.waitForTimeout(200);

    // Verificar que el canvas tiene la clase pointer
    const hasPointerClass = await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      return canvas ? canvas.classList.contains('pointer') : false;
    });

    expect(hasPointerClass).toBe(true);

    // Mover mouse fuera del canvas
    await page.mouse.move(0, 0);
    await page.waitForTimeout(200);

    // Verificar que se removió la clase pointer
    const stillHasPointerClass = await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      return canvas ? canvas.classList.contains('pointer') : false;
    });

    expect(stillHasPointerClass).toBe(false);
  });

  test('grafo debería renderizar paths y links', async () => {
    // Verificar que el canvas tiene contenido dibujado
    const hasContent = await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      const ctx = canvas.getContext('2d');

      // Obtener datos de imagen del canvas
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Verificar si hay píxeles no transparentes (alpha > 0)
      for (let i = 3; i < data.length; i += 4) {
        if (data[i] > 0) return true;
      }
      return false;
    });

    expect(hasContent).toBe(true);
  });

  test('opciones del grafo deberían afectar el renderizado', async () => {
    // Obtener información inicial del canvas
    const initialCanvasData = await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      const ctx = canvas.getContext('2d');
      return ctx.getImageData(0, 0, canvas.width, canvas.height).data;
    });

    // Cambiar opción "Solo primer padre"
    await page.check('#first-parent-only');
    await page.waitForTimeout(1000); // Esperar re-renderizado

    // Obtener nueva información del canvas
    const updatedCanvasData = await page.evaluate(() => {
      const canvas = document.getElementById('commit-graph');
      const ctx = canvas.getContext('2d');
      return ctx.getImageData(0, 0, canvas.width, canvas.height).data;
    });

    // Los datos del canvas deberían ser diferentes
    let isDifferent = false;
    for (let i = 0; i < Math.min(initialCanvasData.length, updatedCanvasData.length); i++) {
      if (initialCanvasData[i] !== updatedCanvasData[i]) {
        isDifferent = true;
        break;
      }
    }

    expect(isDifferent).toBe(true);
  });

  test('commits deberían tener decoradores visibles', async () => {
    // Buscar commits con decoradores
    const commitsWithDecorators = await page.evaluate(() => {
      const commitItems = document.querySelectorAll('#commit-list .commit-item');
      const results = [];

      commitItems.forEach((item, index) => {
        const decorators = item.querySelector('.commit-decorators');
        if (decorators) {
          const decoratorElements = decorators.querySelectorAll('.decorator');
          results.push({
            index,
            sha: item.dataset.sha,
            decoratorCount: decoratorElements.length,
            decorators: Array.from(decoratorElements).map(d => ({
              text: d.textContent,
              className: d.className
            }))
          });
        }
      });

      return results;
    });

    // Debería haber al menos un commit con decoradores (HEAD, branches, etc.)
    expect(commitsWithDecorators.length).toBeGreaterThan(0);

    // Verificar que los decoradores tienen las clases correctas
    for (const commit of commitsWithDecorators) {
      expect(commit.decoratorCount).toBeGreaterThan(0);

      for (const decorator of commit.decorators) {
        expect(decorator.className).toMatch(/decorator\s+(head|branch|remote|tag)/);
        expect(decorator.text.length).toBeGreaterThan(0);
      }
    }
  });

  test('selección de commit debería persistir al hacer scroll', async () => {
    // Seleccionar un commit
    await helper.clickCanvas(page, 16, 30);
    await page.waitForTimeout(500);

    // Verificar que está seleccionado
    let commitsInfo = await helper.getCommitsInfo(page);
    let selectedCommit = commitsInfo.commits.find(c => c.selected);
    expect(selectedCommit).toBeTruthy();

    const selectedSha = selectedCommit.sha;

    // Hacer scroll
    await helper.scrollCommitList(page, 200);
    await page.waitForTimeout(500);

    // Verificar que la selección persiste
    commitsInfo = await helper.getCommitsInfo(page);
    selectedCommit = commitsInfo.commits.find(c => c.selected);
    expect(selectedCommit).toBeTruthy();
    expect(selectedCommit.sha).toBe(selectedSha);
  });
});
