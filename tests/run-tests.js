#!/usr/bin/env node

// Script para ejecutar tests con configuración específica
const { spawn } = require('child_process');
const path = require('path');

const args = process.argv.slice(2);
const testType = args[0] || 'all';

console.log('🧪 Iniciando tests de Gitator...\n');

// Configurar variables de entorno para tests
process.env.NODE_ENV = 'test';
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

let testCommand = ['npx', 'playwright', 'test'];
let testDescription = '';

switch (testType) {
  case 'basic':
    testCommand.push('tests/app.test.js');
    testDescription = 'Tests básicos de la aplicación';
    break;

  case 'graph':
    testCommand.push('tests/git-graph.test.js');
    testDescription = 'Tests del visualizador de grafo';
    break;

  case 'scroll':
    testCommand.push('tests/scroll-sync.test.js');
    testDescription = 'Tests de sincronización de scroll';
    break;

  case 'selection':
    testCommand.push('tests/commit-selection.test.js');
    testDescription = 'Tests de selección de commits';
    break;

  case 'lazy':
    testCommand.push('tests/lazy-loading.test.js');
    testDescription = 'Tests de lazy loading';
    break;

  case 'errors':
    testCommand.push('tests/error-scenarios.test.js');
    testDescription = 'Tests de escenarios de error';
    break;

  case 'complete':
    testCommand.push('tests/gitator-complete.test.js');
    testDescription = 'Tests completos de Gitator (todo en uno)';
    break;

  case 'headed':
    testCommand.push('--headed');
    testDescription = 'Todos los tests (modo visual)';
    break;

  case 'debug':
    testCommand.push('--debug');
    testDescription = 'Tests en modo debug';
    break;

  case 'ui':
    testCommand = ['npx', 'playwright', 'test', '--ui'];
    testDescription = 'Interfaz de usuario de tests';
    break;

  default:
    testDescription = 'Todos los tests';
    break;
}

console.log(`📋 Ejecutando: ${testDescription}`);
console.log(`🔧 Comando: ${testCommand.join(' ')}\n`);

const testProcess = spawn(testCommand[0], testCommand.slice(1), {
  stdio: 'inherit',
  shell: process.platform === 'win32'
});

testProcess.on('close', (code) => {
  console.log(`\n📊 Tests completados con código: ${code}`);

  if (code === 0) {
    console.log('✅ Todos los tests pasaron exitosamente!');
    console.log('\n📈 Para ver el reporte detallado ejecuta: npm run test:report');
  } else {
    console.log('❌ Algunos tests fallaron');
    console.log('\n🔍 Para debuggear ejecuta: npm run test:debug');
    console.log('🎯 Para tests específicos ejecuta:');
    console.log('   node tests/run-tests.js basic     # Tests básicos');
    console.log('   node tests/run-tests.js graph     # Tests del grafo');
    console.log('   node tests/run-tests.js scroll    # Tests de scroll');
    console.log('   node tests/run-tests.js selection # Tests de selección');
    console.log('   node tests/run-tests.js lazy      # Tests de lazy loading');
    console.log('   node tests/run-tests.js errors    # Tests de errores');
    console.log('   node tests/run-tests.js complete  # Tests completos (TODO EN UNO)');
  }

  process.exit(code);
});

testProcess.on('error', (error) => {
  console.error('❌ Error al ejecutar tests:', error);
  process.exit(1);
});
